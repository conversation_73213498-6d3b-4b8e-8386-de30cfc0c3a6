#!/usr/bin/env python3
"""
完全就绪的UI自动化服务启动器
所有依赖在打包时已包含，用户只需点击启动
"""

import os
import sys
import json
import time
import shutil
import platform
import subprocess
import webbrowser
import threading
from pathlib import Path
from tkinter import *
from tkinter import ttk, messagebox, scrolledtext
from tkinter.ttk import Progressbar, Style

class ReadyToUseInstaller:
    def __init__(self):
        self.root = Tk()
        self.setup_appearance()
        
        # 获取打包后的资源路径
        if getattr(sys, 'frozen', False):
            # 运行在PyInstaller打包后的环境
            self.bundle_dir = Path(sys._MEIPASS)
        else:
            # 开发环境
            self.bundle_dir = Path(__file__).parent.parent
            
        # 项目路径（已包含完整依赖）
        self.agent_server_dir = self.bundle_dir / "agent-server"
        self.agent_ui_dir = self.bundle_dir / "agent-chat-ui"
        
        # 服务进程
        self.server_process = None
        self.ui_process = None
        
        self.setup_ui()
        self.setup_styles()
        
    def setup_appearance(self):
        """设置界面外观"""
        self.root.title("UI自动化服务 - 即开即用版")
        
        if sys.platform == "darwin":
            try:
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()
                window_width = 800
                window_height = 600
                x = (screen_width - window_width) // 2
                y = (screen_height - window_height) // 2
                self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
                self.root.configure(bg='#f0f0f0')
                
                # 检测深色模式
                try:
                    result = subprocess.run(['defaults', 'read', '-g', 'AppleInterfaceStyle'], 
                                          capture_output=True, text=True)
                    self.is_dark_mode = result.stdout.strip() == 'Dark'
                except:
                    self.is_dark_mode = False
            except:
                self.is_dark_mode = False
                self.root.geometry("800x600")
        else:
            self.is_dark_mode = False
            self.root.geometry("800x600")
            
        self.root.resizable(True, True)
        
    def setup_styles(self):
        """设置样式"""
        style = Style()
        if sys.platform == "darwin":
            try:
                style.theme_use('aqua')
            except:
                style.theme_use('clam')
        else:
            style.theme_use('clam')
            
    def setup_ui(self):
        """设置用户界面"""
        # 标题区域
        title_frame = Frame(self.root, bg='#007AFF', height=80)
        title_frame.pack(fill=X, padx=0, pady=0)
        title_frame.pack_propagate(False)
        
        title_inner = Frame(title_frame, bg='#007AFF')
        title_inner.pack(expand=True, fill=BOTH, padx=10, pady=10)
        
        title_label = Label(title_inner, text="🚀 UI自动化服务", 
                           bg='#007AFF', fg='white', 
                           font=('SF Pro Display', 18, 'bold'))
        title_label.pack(expand=True)
        
        subtitle_label = Label(title_inner, text="依赖已就绪，一键启动即可使用", 
                              bg='#007AFF', fg='white', 
                              font=('SF Pro Display', 11))
        subtitle_label.pack()
        
        # 主容器
        main_frame = Frame(self.root, bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b')
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 1. 状态显示区域
        self.create_status_section(main_frame)
        
        # 2. 控制按钮区域
        self.create_control_section(main_frame)
        
        # 3. 日志区域
        self.create_log_section(main_frame)
        
        # 4. 状态栏
        self.create_status_bar()
        
    def create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="📊 服务状态")
        status_frame.pack(fill=X, pady=(0, 15))
        
        content_frame = Frame(status_frame, bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b')
        content_frame.pack(fill=X, padx=15, pady=15)
        
        # 状态容器
        status_container = Frame(content_frame, bg='white' if not self.is_dark_mode else '#3b3b3b',
                               relief=SOLID, bd=1)
        status_container.pack(fill=X)
        
        status_inner = Frame(status_container, bg='white' if not self.is_dark_mode else '#3b3b3b')
        status_inner.pack(fill=X, padx=20, pady=15)
        
        # 系统状态
        Label(status_inner, text="📱 系统状态: 所有依赖已就绪", 
              font=('SF Pro Display', 12, 'bold'),
              bg='white' if not self.is_dark_mode else '#3b3b3b',
              fg='#34C759').pack(anchor=W, pady=(0, 10))
        
        # 服务状态
        for i, (service, label_text) in enumerate([('server', 'Agent Server (后端API)'), ('ui', 'Agent UI (前端界面)')]):
            row_frame = Frame(status_inner, bg='white' if not self.is_dark_mode else '#3b3b3b')
            row_frame.pack(fill=X, pady=3)
            
            Label(row_frame, text=f"{label_text}:", 
                  font=('SF Pro Display', 11),
                  bg='white' if not self.is_dark_mode else '#3b3b3b',
                  fg='black' if not self.is_dark_mode else 'white').pack(side=LEFT)
            
            status_label = Label(row_frame, text="● 已停止", fg='#FF3B30', 
                               font=('SF Pro Display', 11),
                               bg='white' if not self.is_dark_mode else '#3b3b3b')
            status_label.pack(side=LEFT, padx=(10, 0))
            
            if service == 'server':
                self.server_status = status_label
            else:
                self.ui_status = status_label
        
    def create_control_section(self, parent):
        """创建控制区域"""
        control_frame = ttk.LabelFrame(parent, text="🎮 服务控制")
        control_frame.pack(fill=X, pady=(0, 15))
        
        content_frame = Frame(control_frame, bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b')
        content_frame.pack(fill=X, padx=15, pady=15)
        
        # 说明文本
        info_label = Label(content_frame, 
                          text="✅ Python依赖已包含  ✅ Node.js模块已包含  ✅ 项目文件已就绪", 
                          font=('SF Pro Display', 10),
                          bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b',
                          fg='#34C759')
        info_label.pack(pady=(0, 15))
        
        # 主要控制按钮
        button_frame = Frame(content_frame, bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b')
        button_frame.pack(fill=X)
        
        self.start_btn = Button(button_frame, text="🚀 启动所有服务", 
                               command=self.start_services, 
                               bg='#007AFF', fg='white', relief=FLAT,
                               font=('SF Pro Display', 14, 'bold'), 
                               padx=40, pady=12)
        self.start_btn.pack(side=LEFT, padx=(0, 15))
        
        self.stop_btn = Button(button_frame, text="⏹️ 停止服务", 
                              command=self.stop_services, 
                              bg='#FF3B30', fg='white', relief=FLAT,
                              font=('SF Pro Display', 14, 'bold'), 
                              padx=40, pady=12, state=DISABLED)
        self.stop_btn.pack(side=LEFT, padx=(0, 15))
        
        self.open_btn = Button(button_frame, text="🌐 打开应用", 
                              command=self.open_app, 
                              bg='#34C759', fg='white', relief=FLAT,
                              font=('SF Pro Display', 14, 'bold'), 
                              padx=40, pady=12, state=DISABLED)
        self.open_btn.pack(side=LEFT)
        
    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="📝 运行日志")
        log_frame.pack(fill=BOTH, expand=True)
        
        content_frame = Frame(log_frame, bg='#f0f0f0' if not self.is_dark_mode else '#2b2b2b')
        content_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(
            content_frame, 
            height=10, 
            font=('SF Mono', 10),
            bg='#1d1f21', 
            fg='#c5c8c6',
            insertbackground='#c5c8c6',
            selectbackground='#373b41',
            relief=SOLID,
            bd=1
        )
        self.log_text.pack(fill=BOTH, expand=True)
        
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = Frame(self.root, bg='#e5e5e5' if not self.is_dark_mode else '#404040', 
                           height=25)
        status_frame.pack(side=BOTTOM, fill=X)
        status_frame.pack_propagate(False)
        
        self.status_bar = Label(status_frame, text="就绪状态 - 可直接启动服务", relief=FLAT, anchor=W, 
                               bg='#e5e5e5' if not self.is_dark_mode else '#404040',
                               fg='black' if not self.is_dark_mode else 'white',
                               font=('SF Pro Display', 10))
        self.status_bar.pack(side=LEFT, fill=X, padx=10, pady=3)
        
    def log(self, message, level='info'):
        """输出日志"""
        timestamp = time.strftime('%H:%M:%S')
        
        colors = {
            'info': '#81a2be',
            'success': '#b5bd68', 
            'error': '#cc6666',
            'warning': '#f0c674'
        }
        
        color = colors.get(level, '#c5c8c6')
        
        self.log_text.insert(END, f"[{timestamp}] ", 'timestamp')
        self.log_text.insert(END, f"{message}\n", level)
        
        self.log_text.tag_config('timestamp', foreground='#969896')
        self.log_text.tag_config('info', foreground='#81a2be')
        self.log_text.tag_config('success', foreground='#b5bd68')
        self.log_text.tag_config('error', foreground='#cc6666')
        self.log_text.tag_config('warning', foreground='#f0c674')
        
        self.log_text.see(END)
        self.status_bar.config(text=message)
        self.root.update()
        
    def start_services(self):
        """启动服务（使用已包含的依赖）"""
        def start_thread():
            self.start_btn.config(state=DISABLED)
            
            try:
                self.log("🚀 开始启动服务...", 'info')
                self.log("📦 使用已包含的依赖，无需安装", 'info')
                
                # 调试信息：显示当前路径和文件结构
                self.log(f"🔍 当前工作目录: {os.getcwd()}", 'info')
                self.log(f"🔍 Agent Server目录: {self.agent_server_dir}", 'info')
                self.log(f"🔍 Agent UI目录: {self.agent_ui_dir}", 'info')
                
                # 检查关键文件是否存在
                server_script = self.agent_server_dir / "start_server.py"
                self.log(f"🔍 Server脚本存在: {server_script.exists()}", 'info')
                
                venv_python = self.agent_server_dir / "venv" / "bin" / "python"
                if not venv_python.exists():
                    venv_python = self.agent_server_dir / "venv" / "Scripts" / "python.exe"
                self.log(f"🔍 虚拟环境Python存在: {venv_python.exists()}", 'info')
                
                node_modules = self.agent_ui_dir / "node_modules"
                self.log(f"🔍 Node.js依赖存在: {node_modules.exists()}", 'info')
                
                # 启动Agent Server（在打包环境中使用合适的Python）
                self.log("🔧 启动Agent Server...", 'info')
                
                # 在打包环境中，优先使用系统Python，因为虚拟环境路径可能无效
                python_cmd = None
                
                # 方案1: 使用系统Python（最可靠）
                possible_python_paths = [
                    "python3",
                    "python",
                    "/usr/bin/python3",
                    "/usr/local/bin/python3",
                    "/opt/homebrew/bin/python3"
                ]
                
                for python_path in possible_python_paths:
                    if shutil.which(python_path):
                        python_cmd = python_path
                        self.log(f"🔧 使用系统Python: {python_path}", 'info')
                        break
                
                if not python_cmd:
                    # 方案2: 尝试当前Python解释器
                    python_cmd = sys.executable
                    self.log(f"🔧 使用当前Python解释器: {python_cmd}", 'info')
                
                # 设置Python路径环境变量，确保能找到依赖
                env = os.environ.copy()
                
                # 将虚拟环境的site-packages添加到PYTHONPATH
                venv_site_packages = None
                if sys.platform == "darwin" or sys.platform.startswith("linux"):
                    venv_site_packages = self.agent_server_dir / "venv" / "lib" / "python3.12" / "site-packages"
                    if not venv_site_packages.exists():
                        # 尝试其他可能的Python版本
                        for version in ["python3.11", "python3.10", "python3.9"]:
                            alt_path = self.agent_server_dir / "venv" / "lib" / version / "site-packages"
                            if alt_path.exists():
                                venv_site_packages = alt_path
                                break
                else:
                    venv_site_packages = self.agent_server_dir / "venv" / "Lib" / "site-packages"
                
                if venv_site_packages and venv_site_packages.exists():
                    env['PYTHONPATH'] = str(venv_site_packages) + os.pathsep + env.get('PYTHONPATH', '')
                    self.log(f"🔧 添加依赖路径: {venv_site_packages}", 'info')
                else:
                    self.log("⚠️ 虚拟环境依赖路径未找到，依赖系统环境", 'warning')
                
                server_script = str(self.agent_server_dir / "start_server.py")
                
                self.log(f"🚀 执行Server命令: {python_cmd} {server_script}", 'info')
                
                # 启动server进程，捕获输出用于调试
                self.server_process = subprocess.Popen(
                    [python_cmd, server_script], 
                    cwd=self.agent_server_dir,
                    env=env,  # 使用包含PYTHONPATH的环境变量
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                time.sleep(3)
                
                # 检查进程是否还在运行
                if self.server_process.poll() is None:
                    self.server_status.config(text="● 运行中", fg='#34C759')
                    self.log("✅ Agent Server启动成功 (端口: 2025)", 'success')
                else:
                    # 进程已退出，获取错误信息
                    stdout, stderr = self.server_process.communicate()
                    self.server_status.config(text="● 已停止", fg='#FF453A')
                    self.log("❌ Agent Server启动失败，进程已退出", 'error')
                    if stderr:
                        self.log(f"错误信息: {stderr}", 'error')
                    if stdout:
                        self.log(f"输出信息: {stdout}", 'info')
                    raise Exception(f"Agent Server启动失败，退出码: {self.server_process.returncode}")
                
                # 启动Agent UI（使用已包含的node_modules）
                self.log("🔧 启动Agent UI...", 'info')
                
                # 检查node_modules是否存在
                node_modules = self.agent_ui_dir / "node_modules"
                if node_modules.exists():
                    self.log("✅ 使用已包含的Node.js依赖", 'success')
                else:
                    self.log("❌ Node.js依赖目录不存在", 'error')
                    raise Exception("Node.js依赖未包含在应用中")
                
                # 使用更可靠的方式启动Next.js - 直接使用Node.js + next模块
                node_cmd = None
                possible_node_paths = [
                    "node",
                    "/usr/local/bin/node", 
                    "/opt/homebrew/bin/node",
                    "/usr/bin/node",
                ]
                
                for node_path in possible_node_paths:
                    if shutil.which(node_path):
                        node_cmd = node_path
                        self.log(f"🔧 找到Node.js: {node_path}", 'info')
                        break
                
                if not node_cmd:
                    raise Exception("未找到Node.js运行环境，请确保系统已安装Node.js")
                
                # 支持pnpm和npm结构启动Next.js
                self.log("🔧 使用Node.js直接运行Next.js", 'info')
                
                # 检查pnpm结构中的next模块
                next_module = None
                pnpm_dir = node_modules / ".pnpm"
                
                if pnpm_dir.exists():
                    self.log("🔧 检测到pnpm结构，搜索Next.js模块", 'info')
                    # 在.pnpm目录中寻找next模块
                    for pnpm_package in pnpm_dir.iterdir():
                        if pnpm_package.name.startswith("next@"):
                            potential_next = pnpm_package / "node_modules" / "next"
                            if potential_next.exists():
                                next_module = potential_next
                                self.log(f"🔧 找到pnpm的Next.js模块: {next_module}", 'info')
                                break
                
                # 如果没找到pnpm结构的next，检查传统npm结构
                if not next_module:
                    traditional_next = node_modules / "next"
                    if traditional_next.exists():
                        next_module = traditional_next
                        self.log("🔧 找到传统结构的Next.js模块", 'info')
                
                # 启动Next.js - 使用生产模式启动已构建的应用
                if next_module:
                    # 检查是否有构建产物
                    build_dir = self.agent_ui_dir / ".next"
                    if build_dir.exists():
                        self.log("🔧 使用next start启动生产应用", 'info')
                        # 尝试使用next的CLI
                        next_cli = next_module / "cli.js"
                        if next_cli.exists():
                            ui_cmd = [node_cmd, str(next_cli), "start"]
                        else:
                            # 使用require方式启动生产模式
                            self.log("🔧 使用require方式启动生产应用", 'info')
                            next_start_script = f'''
const {{ createServer }} = require('http');
const next = require('next');
const path = require('path');
const projectDir = "{str(self.agent_ui_dir)}";
const app = next({{ dev: false, dir: projectDir }});
const handle = app.getRequestHandler();

app.prepare().then(() => {{
  createServer((req, res) => {{
    handle(req, res);
  }}).listen(3000, (err) => {{
    if (err) throw err;
    console.log('> Ready on http://localhost:3000');
  }});
}});
'''
                            ui_cmd = [node_cmd, "-e", next_start_script]
                    else:
                        self.log("❌ 未找到Next.js构建产物，尝试开发模式", 'warning')
                        # 回退到开发模式
                        next_cli = next_module / "cli.js"
                        if next_cli.exists():
                            ui_cmd = [node_cmd, str(next_cli), "dev"]
                        else:
                            next_dev_script = f'''
const {{ createServer }} = require('http');
const next = require('next');
const path = require('path');
const projectDir = "{str(self.agent_ui_dir)}";
const dev = true;
const app = next({{ dev, dir: projectDir }});
const handle = app.getRequestHandler();

app.prepare().then(() => {{
  createServer((req, res) => {{
    handle(req, res);
  }}).listen(3000, (err) => {{
    if (err) throw err;
    console.log('> Ready on http://localhost:3000');
  }});
}});
'''
                            ui_cmd = [node_cmd, "-e", next_dev_script]
                else:
                    self.log("❌ 未找到Next.js模块", 'error')
                    raise Exception("Next.js模块未找到，请确保已正确安装依赖")
                
                # 设置环境变量（支持pnpm和npm结构）
                ui_env = os.environ.copy()
                # 如果有构建产物，设置为生产模式
                if (self.agent_ui_dir / ".next").exists():
                    ui_env['NODE_ENV'] = 'production'
                    self.log("🔧 设置环境为生产模式", 'info')
                else:
                    ui_env['NODE_ENV'] = 'development'
                    self.log("🔧 设置环境为开发模式", 'info')
                ui_env['PATH'] = str(node_modules / ".bin") + os.pathsep + ui_env.get('PATH', '')
                
                # 设置NODE_PATH支持pnpm结构
                node_paths = [str(node_modules)]
                if pnpm_dir.exists():
                    self.log("🔧 配置pnpm模块路径", 'info')
                    # 添加.pnpm下所有包的node_modules路径
                    for pnpm_package in pnpm_dir.iterdir():
                        if pnpm_package.is_dir():
                            pnpm_node_modules = pnpm_package / "node_modules"
                            if pnpm_node_modules.exists():
                                node_paths.append(str(pnpm_node_modules))
                
                ui_env['NODE_PATH'] = os.pathsep.join(node_paths)
                self.log(f"🔧 设置NODE_PATH: {len(node_paths)}个路径", 'info')
                
                self.log(f"🚀 执行命令: {' '.join(ui_cmd)}", 'info')
                
                # 启动UI进程，捕获输出用于调试
                self.ui_process = subprocess.Popen(
                    ui_cmd, 
                    cwd=self.agent_ui_dir, 
                    env=ui_env,  # 使用UI专用的环境变量
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                time.sleep(5)
                
                # 检查进程是否还在运行
                if self.ui_process.poll() is None:
                    self.ui_status.config(text="● 运行中", fg='#34C759')
                    self.log("✅ Agent UI启动成功 (端口: 3000)", 'success')
                else:
                    # 进程已退出，获取错误信息
                    stdout, stderr = self.ui_process.communicate()
                    self.ui_status.config(text="● 已停止", fg='#FF453A')
                    self.log("❌ Agent UI启动失败，进程已退出", 'error')
                    if stderr:
                        self.log(f"错误信息: {stderr}", 'error')
                    if stdout:
                        self.log(f"输出信息: {stdout}", 'info')
                    raise Exception(f"Agent UI启动失败，退出码: {self.ui_process.returncode}")
                
                # 更新按钮状态
                self.stop_btn.config(state=NORMAL)
                self.open_btn.config(state=NORMAL)
                
                self.log("🎉 所有服务启动完成！", 'success')
                self.log("🌐 Web界面: http://localhost:3000", 'info')
                self.log("📡 API服务: http://localhost:2025", 'info')
                
                # 询问是否自动打开
                self.root.after(2000, self.ask_open_browser)
                
            except Exception as e:
                self.log(f"❌ 服务启动失败: {e}", 'error')
                self.start_btn.config(state=NORMAL)
                
        threading.Thread(target=start_thread, daemon=True).start()
        
    def ask_open_browser(self):
        """询问是否打开浏览器"""
        if messagebox.askyesno("服务已启动", "所有服务已成功启动！\n是否立即打开Web界面？"):
            self.open_app()
            
    def stop_services(self):
        """停止服务"""
        self.log("🛑 正在停止所有服务...", 'info')
        
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                self.server_process = None
                self.server_status.config(text="● 已停止", fg='#FF3B30')
                self.log("✅ Agent Server已停止", 'success')
            
            if self.ui_process:
                self.ui_process.terminate()
                self.ui_process.wait(timeout=5)
                self.ui_process = None
                self.ui_status.config(text="● 已停止", fg='#FF3B30')
                self.log("✅ Agent UI已停止", 'success')
            
            # 更新按钮状态
            self.start_btn.config(state=NORMAL)
            self.stop_btn.config(state=DISABLED)
            self.open_btn.config(state=DISABLED)
            
            self.log("🎉 所有服务已停止", 'success')
            
        except Exception as e:
            self.log(f"❌ 停止服务时出错: {e}", 'error')
            
    def open_app(self):
        """打开应用"""
        try:
            webbrowser.open('http://localhost:3000')
            self.log("🌐 Web界面已在浏览器中打开", 'success')
        except Exception as e:
            self.log(f"❌ 打开浏览器失败: {e}", 'error')
            
    def on_closing(self):
        """关闭程序时的处理"""
        if messagebox.askokcancel("退出", "确定要退出程序吗？\n正在运行的服务将被停止。"):
            self.stop_services()
            self.root.destroy()
            
    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 启动欢迎信息
        self.log("🚀 UI自动化服务启动器已就绪", 'success')
        self.log("✅ 所有依赖已包含在应用中", 'success')
        self.log("💡 点击'启动所有服务'即可开始使用", 'info')
        
        # 检查依赖状态
        self.check_dependencies()
        
        self.root.mainloop()
        
    def check_dependencies(self):
        """检查已包含的依赖"""
        self.log("🔍 检查已包含的依赖...", 'info')
        
        # 检查agent-server
        if self.agent_server_dir.exists():
            self.log(f"✅ Agent Server项目已包含: {self.agent_server_dir}", 'success')
            
            venv_dir = self.agent_server_dir / "venv"
            if venv_dir.exists():
                self.log("✅ Python虚拟环境已包含", 'success')
            else:
                self.log("⚠️ Python虚拟环境未包含，将使用系统环境", 'warning')
        else:
            self.log("❌ Agent Server项目未找到", 'error')
        
        # 检查agent-chat-ui
        if self.agent_ui_dir.exists():
            self.log(f"✅ Agent UI项目已包含: {self.agent_ui_dir}", 'success')
            
            node_modules = self.agent_ui_dir / "node_modules"
            if node_modules.exists():
                self.log("✅ Node.js依赖已包含", 'success')
            else:
                self.log("⚠️ Node.js依赖未包含，需要运行时安装", 'warning')
        else:
            self.log("❌ Agent UI项目未找到", 'error')
            
        self.log("📋 依赖检查完成，可以启动服务", 'info')

def main():
    """主函数"""
    try:
        app = ReadyToUseInstaller()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败:\n{e}")

if __name__ == "__main__":
    main()
