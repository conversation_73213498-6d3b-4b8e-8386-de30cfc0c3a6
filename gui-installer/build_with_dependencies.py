#!/usr/bin/env python3
"""
打包时包含所有依赖的构建脚本
确保agent-server的venv和agent-chat-ui的node_modules都被包含
用户使用时无需安装任何依赖
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

class DependencyPackageBuilder:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.project_root = self.base_dir.parent
        self.agent_server_dir = self.project_root / "agent-server"
        self.agent_ui_dir = self.project_root / "agent-chat-ui"
        
    def log(self, message):
        """输出日志"""
        print(f"[构建] {message}")
        
    def prepare_python_dependencies(self):
        """准备Python依赖（创建包含所有依赖的venv）"""
        self.log("🐍 准备Python依赖...")
        
        if not self.agent_server_dir.exists():
            raise Exception(f"找不到agent-server目录: {self.agent_server_dir}")
            
        venv_dir = self.agent_server_dir / "venv"
        
        # 如果venv不存在或需要重新创建
        if not venv_dir.exists():
            self.log("📦 创建Python虚拟环境...")
            
            python_cmd = "python3" if shutil.which("python3") else "python"
            subprocess.run([python_cmd, "-m", "venv", str(venv_dir)], 
                          cwd=self.agent_server_dir, check=True)
            
            # 安装依赖
            if sys.platform == "darwin" or sys.platform.startswith("linux"):
                pip_cmd = str(venv_dir / "bin" / "pip")
            else:
                pip_cmd = str(venv_dir / "Scripts" / "pip.exe")
                
            requirements_file = self.agent_server_dir / "requirements.txt"
            if requirements_file.exists():
                self.log("📦 安装Python依赖...")
                subprocess.run([pip_cmd, "install", "-r", str(requirements_file)], 
                              check=True)
            else:
                self.log("⚠️ 未找到requirements.txt，跳过依赖安装")
                
        else:
            self.log("✅ Python虚拟环境已存在")
            
        # 验证虚拟环境
        if sys.platform == "darwin" or sys.platform.startswith("linux"):
            python_exe = venv_dir / "bin" / "python"
        else:
            python_exe = venv_dir / "Scripts" / "python.exe"
            
        if python_exe.exists():
            self.log("✅ Python虚拟环境准备完成")
            return True
        else:
            self.log("❌ Python虚拟环境创建失败")
            return False
            
    def prepare_nodejs_dependencies(self):
        """检查Node.js依赖（使用已存在的依赖，支持pnpm结构）"""
        self.log("📦 检查Node.js依赖...")
        
        if not self.agent_ui_dir.exists():
            raise Exception(f"找不到agent-chat-ui目录: {self.agent_ui_dir}")
            
        node_modules_dir = self.agent_ui_dir / "node_modules"
        package_json = self.agent_ui_dir / "package.json"
        
        if not package_json.exists():
            self.log("⚠️ 未找到package.json")
            return False
            
        # 只检查node_modules是否存在，不重新安装
        if node_modules_dir.exists() and any(node_modules_dir.iterdir()):
            self.log("✅ 使用已存在的Node.js依赖")
            
            # 检查是否是pnpm结构
            pnpm_dir = node_modules_dir / ".pnpm"
            if pnpm_dir.exists():
                self.log("✅ 检测到pnpm结构，将在启动时正确处理")
            else:
                self.log("✅ npm结构")
            
            return True
        else:
            self.log("❌ Node.js依赖目录不存在或为空")
            self.log("💡 请先在agent-chat-ui目录运行 'pnpm install' 或 'npm install' 安装依赖")
            return False
            
    def install_pyinstaller(self):
        """安装PyInstaller"""
        try:
            import PyInstaller
            self.log("✅ PyInstaller已安装")
            return True
        except ImportError:
            self.log("📦 安装PyInstaller...")
            try:
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'], check=True)
                self.log("✅ PyInstaller安装完成")
                return True
            except subprocess.CalledProcessError as e:
                self.log(f"❌ PyInstaller安装失败: {e}")
                return False
                
    def create_pyinstaller_spec(self):
        """创建PyInstaller配置文件"""
        self.log("📝 创建PyInstaller配置文件...")
        
        spec_content = f'''
# -*- mode: python ; coding: utf-8 -*-
import os
from pathlib import Path

block_cipher = None

def filter_files(src, names):
    """过滤掉可能导致问题的文件和目录"""
    excluded = []
    for name in names:
        # 排除可能有问题的文件和目录
        if (name.startswith('.') and name not in ['.next', '.bin']) or \\
           name in ['__pycache__', '.git', '.vscode', 'node_modules/.cache'] or \\
           name.endswith('.log') or \\
           name.endswith('.tmp'):
            excluded.append(name)
    return excluded

a = Analysis(
    ['installer_ready_to_use.py'],
    pathex=[],
    binaries=[],
    datas=[
        # 包含整个agent-server目录（包括venv）
        ('{self.agent_server_dir}', 'agent-server'),
        # 包含整个agent-chat-ui目录（包括node_modules）
        ('{self.agent_ui_dir}', 'agent-chat-ui'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'tkinter.messagebox',
        'threading',
        'webbrowser',
        'subprocess',
        'pathlib',
        'shutil',
        'json',
        'time',
        'platform',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[
        # 排除大型不必要的库
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'IPython',
        'jupyter',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'django',
        'tornado',
        'twisted',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='UI自动化服务',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='UI自动化服务',
)

# Mac应用包
app = BUNDLE(
    coll,
    name='UI自动化服务.app',
    icon=None,
    bundle_identifier='com.ui-automation.ready',
    version='1.0.0',
    info_plist={{
        'CFBundleName': 'UI自动化服务',
        'CFBundleDisplayName': 'UI自动化服务',
        'CFBundleIdentifier': 'com.ui-automation.ready',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'NSHighResolutionCapable': True,
        'LSMinimumSystemVersion': '10.13',
    }},
)
'''
        
        with open('ready_to_use.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content.strip())
            
        self.log("✅ PyInstaller配置文件已创建")
        
    def fix_problematic_files(self):
        """修复可能导致PyInstaller出错的文件"""
        self.log("🔧 修复问题文件...")
        
        # 检查并修复node_modules中的问题符号链接
        node_modules = self.agent_ui_dir / "node_modules"
        if node_modules.exists():
            # 查找并修复断开的符号链接
            for root, dirs, files in os.walk(node_modules):
                root_path = Path(root)
                # 跳过.pnpm目录中的某些问题文件
                if ".pnpm" in str(root_path):
                    for file in files:
                        file_path = root_path / file
                        if file_path.is_symlink() and not file_path.exists():
                            try:
                                file_path.unlink()
                                self.log(f"🗑️ 删除断开的符号链接: {file_path}")
                            except:
                                pass
                                
                # 删除可能有问题的缓存目录
                for dir_name in dirs[:]:  # 使用切片复制避免修改正在迭代的列表
                    if dir_name in ['.cache', '.tmp', '__pycache__']:
                        problematic_dir = root_path / dir_name
                        try:
                            shutil.rmtree(problematic_dir)
                            self.log(f"🗑️ 删除缓存目录: {problematic_dir}")
                            dirs.remove(dir_name)
                        except:
                            pass
        
    def build_package(self):
        """构建包"""
        self.log("🔨 开始构建包含所有依赖的应用...")
        self.log("⏳ 这可能需要较长时间，请耐心等待...")
        
        # 清理之前的构建
        for dir_name in ['build', 'dist']:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
                self.log(f"🗑️ 清理{dir_name}目录")
        
        # 运行PyInstaller，增加错误处理
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'ready_to_use.spec']
        
        # 先尝试直接构建
        self.log("🔧 第一次尝试构建...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        # 如果失败且是文件路径问题，尝试修复
        if result.returncode != 0 and "No such file or directory" in result.stderr:
            self.log("⚠️ 检测到文件路径问题，尝试修复...")
            self.fix_problematic_files()
            
            # 重新尝试构建
            self.log("🔧 第二次尝试构建...")
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            self.log("✅ 应用构建成功！")
            
            # 检查生成的文件
            if sys.platform == "darwin":
                app_path = Path("dist/UI自动化服务.app")
                if app_path.exists():
                    self.log(f"📱 Mac应用: {app_path.absolute()}")
                    
            exe_path = Path("dist/UI自动化服务")
            if exe_path.exists():
                self.log(f"💻 可执行文件: {exe_path.absolute()}")
                
            return True
        else:
            self.log(f"❌ 构建失败:")
            self.log(result.stderr)
            return False
            
    def verify_package(self):
        """验证打包结果"""
        self.log("🔍 验证打包结果...")
        
        if sys.platform == "darwin":
            app_path = Path("dist/UI自动化服务.app")
            if app_path.exists():
                # 检查是否包含了依赖
                server_venv = app_path / "Contents/MacOS/agent-server/venv"
                ui_modules = app_path / "Contents/MacOS/agent-chat-ui/node_modules"
                
                if server_venv.exists():
                    self.log("✅ Python虚拟环境已包含在应用中")
                else:
                    self.log("⚠️ Python虚拟环境可能未包含")
                    
                if ui_modules.exists():
                    self.log("✅ Node.js依赖已包含在应用中")
                else:
                    self.log("⚠️ Node.js依赖可能未包含")
                    
        exe_path = Path("dist/UI自动化服务")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / 1024 / 1024
            self.log(f"📏 应用大小: {size_mb:.1f} MB")
            
    def create_usage_guide(self):
        """创建使用指南"""
        guide_content = '''# UI自动化服务 - 即开即用版

## 🎯 特色功能
✅ **所有依赖已包含** - Python虚拟环境和Node.js模块都已内置
✅ **即开即用** - 双击启动，点击按钮直接运行服务
✅ **无需安装** - 不需要预装Python、Node.js或任何依赖
✅ **一键操作** - 启动、停止、打开Web界面都是一键完成

## 🚀 使用方法

### 启动应用
1. 双击 `UI自动化服务.app` (Mac) 或 `UI自动化服务` (其他系统)
2. 如遇安全提示，右键选择"打开"

### 使用服务
1. 点击 "🚀 启动所有服务"
2. 等待服务启动完成（约10-15秒）
3. 点击 "🌐 打开应用" 或访问 http://localhost:3000
4. 开始使用UI自动化功能

### 停止服务
1. 点击 "⏹️ 停止服务" 
2. 或直接关闭应用窗口

## 📋 技术说明

### 服务端口
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:2025

### 内置依赖
- **Python环境**: 完整的虚拟环境已包含所有必需库
- **Node.js模块**: 所有前端依赖都已预装
- **项目文件**: agent-server和agent-chat-ui完整代码

### 系统要求
- macOS 10.13+ / Windows 10+ / Linux
- 可用内存: 2GB+
- 磁盘空间: 应用本身约500MB-1GB

## 🔧 故障排除

**Q: 启动很慢？**
A: 首次启动需要解压内置依赖，请耐心等待

**Q: 端口被占用？**
A: 确保2025和3000端口未被占用，或重启计算机

**Q: 服务启动失败？**
A: 查看应用内日志，检查系统资源是否充足

## 💡 优势说明

相比需要安装依赖的版本，此版本：
- ✅ 用户体验更好 - 真正的"开箱即用"
- ✅ 部署更简单 - 只需分发一个文件
- ✅ 环境更稳定 - 避免环境冲突问题
- ✅ 维护更容易 - 依赖版本固定

---
注意: 由于包含了完整依赖，文件大小较大，但换来了极佳的用户体验。
'''
        
        with open('使用指南-即开即用版.txt', 'w', encoding='utf-8') as f:
            f.write(guide_content)
            
        self.log("✅ 使用指南已创建")
        
    def build(self):
        """完整构建流程"""
        self.log("🚀 开始构建包含所有依赖的UI自动化服务")
        self.log("=" * 60)
        
        try:
            # 1. 安装PyInstaller
            if not self.install_pyinstaller():
                return False
                
            # 2. 准备Python依赖
            if not self.prepare_python_dependencies():
                return False
                
            # 3. 准备Node.js依赖
            if not self.prepare_nodejs_dependencies():
                return False
                
            # 4. 创建PyInstaller配置
            self.create_pyinstaller_spec()
            
            # 5. 构建应用
            if not self.build_package():
                return False
                
            # 6. 验证结果
            self.verify_package()
            
            # 7. 创建使用指南
            self.create_usage_guide()
            
            self.log("\n" + "=" * 60)
            self.log("🎉 构建完成！")
            self.log("📦 用户现在可以直接运行应用，无需安装任何依赖")
            self.log("🎯 真正实现了'双击启动，一键使用'")
            
            return True
            
        except Exception as e:
            self.log(f"❌ 构建失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    if not Path("installer_ready_to_use.py").exists():
        print("❌ 请在gui-installer目录下运行此脚本")
        return
        
    builder = DependencyPackageBuilder()
    
    print("📋 即将构建包含所有依赖的应用")
    print("这将包括:")
    print("  - Python虚拟环境 (venv)")
    print("  - Node.js依赖 (node_modules)")
    print("  - 完整的项目代码")
    print()
    
    confirm = input("确认开始构建？这可能需要10-20分钟 (y/n): ")
    if confirm.lower() not in ['y', 'yes', '是']:
        print("构建已取消")
        return
        
    try:
        success = builder.build()
        
        if success:
            print("\n🎉 构建成功！")
            print("🎯 用户现在真正可以'开箱即用'")
            
            # 询问是否测试
            test = input("\n🔍 是否立即测试应用？(y/n): ")
            if test.lower() in ['y', 'yes', '是']:
                if sys.platform == "darwin":
                    app_path = Path("dist/UI自动化服务.app")
                    if app_path.exists():
                        print("🚀 启动测试...")
                        subprocess.run(['open', str(app_path)])
                else:
                    exe_path = Path("dist/UI自动化服务")
                    if exe_path.exists():
                        print("🚀 启动测试...")
                        subprocess.run([str(exe_path)])
        else:
            print("❌ 构建失败")
            
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
